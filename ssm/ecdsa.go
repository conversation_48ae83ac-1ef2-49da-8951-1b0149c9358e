// Package ssm 提供安全签名模块(Secure Signature Module)的实现
// 该包实现了ECDSA椭圆曲线数字签名算法，为区块链应用提供密钥生成、消息签名和签名验证功能
package ssm

import (
	"encoding/hex"
	"fmt"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/log"
)

// CreateECDSAKeyPair 生成ECDSA椭圆曲线密钥对
// 该函数使用secp256k1椭圆曲线生成一对公私钥，这是比特币和以太坊使用的标准曲线
//
// 返回值:
//   - string: 私钥的十六进制字符串表示（64字节，256位）
//   - string: 公钥的十六进制字符串表示（未压缩格式，65字节）
//   - string: 压缩公钥的十六进制字符串表示（33字节，节省存储空间）
//   - error: 密钥生成过程中的错误信息
//
// 安全注意事项:
//   - 使用加密安全的随机数生成器确保私钥的随机性
//   - 私钥必须妥善保管，泄露将导致资产损失
//   - 公钥可以安全地公开分享，用于接收资金和验证签名
//   - secp256k1曲线提供128位安全强度，满足当前密码学安全要求
func CreateECDSAKeyPair() (string, string, string, error) {
	// 使用以太坊的crypto包生成secp256k1椭圆曲线密钥对
	// 该函数内部使用加密安全的随机数生成器
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		log.Error("generate key fail", "err", err)
		return EmptyHexString, EmptyHexString, EmptyHexString, err
	}

	// 将私钥转换为十六进制字符串格式
	// 私钥是32字节的随机数，表示椭圆曲线上的标量
	priKeyStr := hex.EncodeToString(crypto.FromECDSA(privateKey))

	// 将公钥转换为十六进制字符串格式（未压缩格式）
	// 未压缩公钥包含x和y坐标，总共65字节（1字节前缀 + 32字节x + 32字节y）
	pubKeyStr := hex.EncodeToString(crypto.FromECDSAPub(&privateKey.PublicKey))

	// 将公钥转换为压缩格式的十六进制字符串
	// 压缩公钥只包含x坐标和y坐标的奇偶性，总共33字节，节省存储空间
	compressPubkeyStr := hex.EncodeToString(crypto.CompressPubkey(&privateKey.PublicKey))

	return priKeyStr, pubKeyStr, compressPubkeyStr, nil
}

// SignECDSAMessage 使用ECDSA算法对消息哈希进行数字签名
// 该函数实现了ECDSA数字签名算法，用于对区块链交易或其他消息进行签名
//
// 参数:
//   - privKey: 私钥的十六进制字符串表示（64字符，32字节）
//   - txMsg: 待签名消息的哈希值，十六进制字符串格式
//
// 返回值:
//   - string: ECDSA签名的十六进制字符串表示（130字符，65字节）
//   - error: 签名过程中的错误信息
//
// 签名格式说明:
//   - ECDSA签名包含三个部分：r值（32字节）、s值（32字节）、恢复ID（1字节）
//   - 恢复ID用于从签名和消息哈希中恢复公钥，这是以太坊的扩展
//   - 签名结果可以被任何人使用对应的公钥进行验证
//
// 安全注意事项:
//   - 私钥必须保密，不能泄露给任何第三方
//   - 每次签名都会使用随机数k，确保签名的不可预测性
//   - 消息哈希应该是经过安全哈希函数（如SHA-256）处理的结果
func SignECDSAMessage(privKey string, txMsg string) (string, error) {
	// 将十六进制消息哈希转换为32字节的哈希值
	// 这确保了输入的一致性和安全性
	hash := common.HexToHash(txMsg)
	fmt.Println(hash.Hex()) // 调试输出，生产环境中应该移除

	// 将十六进制私钥字符串解码为字节数组
	privByte, err := hex.DecodeString(privKey)
	if err != nil {
		log.Error("decode private key fail", "err", err)
		return EmptyHexString, err
	}

	// 将字节数组转换为ECDSA私钥对象
	// 这个过程会验证私钥的有效性
	privKeyEcdsa, err := crypto.ToECDSA(privByte)
	if err != nil {
		log.Error("Byte private key to ecdsa key fail", "err", err)
		return EmptyHexString, err
	}

	// 执行ECDSA签名操作
	// crypto.Sign函数实现了完整的ECDSA签名算法，包括随机数生成
	// 返回的签名包含r、s值和恢复ID，总共65字节
	signatureByte, err := crypto.Sign(hash[:], privKeyEcdsa)
	if err != nil {
		log.Error("sign transaction fail", "err", err)
		return EmptyHexString, err
	}

	// 将签名字节数组转换为十六进制字符串返回
	return hex.EncodeToString(signatureByte), nil
}

// VerifyEcdsaSignature 验证ECDSA数字签名的有效性
// 该函数用于验证给定的ECDSA签名是否由对应的私钥对指定消息哈希生成
//
// 参数:
//   - publicKey: 公钥的十六进制字符串表示（130字符，65字节未压缩格式）
//   - txHash: 原始消息哈希的十六进制字符串表示
//   - signature: ECDSA签名的十六进制字符串表示（130字符，65字节）
//
// 返回值:
//   - bool: 签名验证结果，true表示签名有效，false表示签名无效
//   - error: 验证过程中的错误信息，如参数格式错误等
//
// 验证原理:
//   - ECDSA签名验证是一个数学过程，不需要私钥参与
//   - 验证过程使用椭圆曲线数学运算确认签名的有效性
//   - 只有使用对应私钥生成的签名才能通过验证
//
// 安全注意事项:
//   - 签名验证是公开操作，任何人都可以执行
//   - 验证成功证明签名者拥有对应的私钥，确保了不可否认性
//   - 该函数不使用恢复ID，适用于标准的ECDSA签名验证场景
func VerifyEcdsaSignature(publicKey, txHash, signature string) (bool, error) {
	// 将十六进制公钥字符串转换为字节数组
	// 公钥用于验证签名的有效性
	pubKeyBytes, err := hex.DecodeString(publicKey)
	if err != nil {
		log.Error("Error converting public key to bytes", err)
		return false, err
	}

	// 将十六进制交易哈希字符串转换为字节数组
	// 这是被签名的原始消息哈希
	txHashBytes, err := hex.DecodeString(txHash)
	if err != nil {
		log.Error("Error converting transaction hash to bytes", err)
		return false, err
	}

	// 将十六进制签名字符串转换为字节数组
	// 签名包含r值、s值和恢复ID
	sigBytes, err := hex.DecodeString(signature)
	if err != nil {
		log.Error("Error converting signature to bytes", err)
		return false, err
	}

	// 执行ECDSA签名验证
	// 注意：这里只使用签名的前64字节（r和s值），不包括恢复ID
	// crypto.VerifySignature函数实现了标准的ECDSA验证算法
	return crypto.VerifySignature(pubKeyBytes, txHashBytes, sigBytes[:64]), nil
}
