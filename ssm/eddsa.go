// Package ssm 提供EdDSA (Edwards-curve Digital Signature Algorithm) 数字签名算法实现
// EdDSA是一种基于Edwards曲线的现代数字签名算法，具有更好的性能和安全性
package ssm

import (
	"crypto/ed25519"
	"crypto/rand"
	"encoding/hex"

	"github.com/ethereum/go-ethereum/log"
)

// CreateEdDSAKeyPair 生成EdDSA密钥对
// 该函数使用Ed25519椭圆曲线生成一对公私钥，Ed25519是EdDSA的一个具体实现
//
// 返回值:
//   - string: 私钥的十六进制字符串表示（128字符，64字节）
//   - string: 公钥的十六进制字符串表示（64字符，32字节）
//   - error: 密钥生成过程中的错误信息
//
// EdDSA vs ECDSA 优势:
//   - 更快的签名和验证速度
//   - 更小的签名大小（64字节 vs 65字节）
//   - 更强的安全性，抗侧信道攻击
//   - 确定性签名，相同消息和私钥总是产生相同签名
//   - 不需要安全的随机数生成器进行签名
//
// 安全注意事项:
//   - Ed25519提供128位安全强度，满足现代密码学要求
//   - 私钥必须妥善保管，泄露将导致安全风险
//   - 公钥可以安全地公开分享
//   - Ed25519曲线经过精心设计，避免了许多常见的实现陷阱
func CreateEdDSAKeyPair() (string, string, error) {
	// 使用Go标准库的ed25519包生成密钥对
	// ed25519.GenerateKey使用加密安全的随机数生成器
	// 返回的公钥是32字节，私钥是64字节（包含32字节种子和32字节公钥）
	publicKey, privateKey, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		log.Error("create key pair fail:", "err", err)
		return EmptyHexString, EmptyHexString, nil
	}

	// 将私钥和公钥转换为十六进制字符串格式
	// Ed25519私钥是64字节，包含32字节的种子和对应的公钥
	// Ed25519公钥是32字节，比ECDSA公钥更紧凑
	return hex.EncodeToString(privateKey), hex.EncodeToString(publicKey), nil
}

// SignEdDSAMessage 使用EdDSA算法对消息进行数字签名
// 该函数实现了Ed25519数字签名算法，提供确定性签名和高性能
//
// 参数:
//   - priKey: 私钥的十六进制字符串表示（128字符，64字节）
//   - txMsg: 待签名消息的十六进制字符串表示（可以是任意长度的消息）
//
// 返回值:
//   - string: EdDSA签名的十六进制字符串表示（128字符，64字节）
//   - error: 签名过程中的错误信息
//
// EdDSA签名特点:
//   - 确定性签名：相同的私钥和消息总是产生相同的签名
//   - 高性能：签名和验证速度比ECDSA更快
//   - 安全性：抗侧信道攻击，不需要安全的随机数生成器
//   - 紧凑性：签名大小固定为64字节，比ECDSA略小
//
// 安全注意事项:
//   - 私钥必须保密，不能泄露给任何第三方
//   - EdDSA不需要随机数，避免了随机数生成器的安全风险
//   - 可以直接对原始消息签名，不需要预先哈希（但这里接受哈希值以保持接口一致性）
func SignEdDSAMessage(priKey string, txMsg string) (string, error) {
	// 将十六进制私钥字符串解码为字节数组
	// Ed25519私钥是64字节，包含32字节种子和32字节公钥
	privateKey, err := hex.DecodeString(priKey)
	if err != nil {
		log.Error("Decode private key string fail", "err", err)
		return "", err
	}

	// 将十六进制消息字符串解码为字节数组
	// EdDSA可以直接对任意长度的消息进行签名
	txMsgByte, err := hex.DecodeString(txMsg)
	if err != nil {
		log.Error("Decode tx message fail", "err", err)
		return "", err
	}

	// 执行Ed25519签名操作
	// ed25519.Sign函数实现了完整的EdDSA签名算法
	// 返回的签名是64字节，包含R点（32字节）和s标量（32字节）
	signMsg := ed25519.Sign(privateKey, txMsgByte)

	// 将签名字节数组转换为十六进制字符串返回
	return hex.EncodeToString(signMsg), nil
}

// VerifyEdDSASign 验证EdDSA数字签名的有效性
// 该函数用于验证给定的EdDSA签名是否由对应的私钥对指定消息生成
//
// 参数:
//   - pubKey: 公钥的十六进制字符串表示（64字符，32字节）
//   - msgHash: 原始消息的十六进制字符串表示
//   - sig: EdDSA签名的十六进制字符串表示（128字符，64字节）
//
// 返回值:
//   - bool: 签名验证结果，true表示签名有效，false表示签名无效
//
// EdDSA验证特点:
//   - 高性能：验证速度比ECDSA更快
//   - 确定性：相同的输入总是产生相同的验证结果
//   - 安全性：抗侧信道攻击，验证过程更安全
//   - 简洁性：验证算法实现更简单，不易出错
//
// 安全注意事项:
//   - 签名验证是公开操作，任何人都可以执行
//   - 验证成功证明签名者拥有对应的私钥
//   - EdDSA验证不需要复杂的数学运算，降低了实现错误的风险
//   - 注意：此函数忽略了解码错误，生产环境中应该处理这些错误
func VerifyEdDSASign(pubKey, msgHash, sig string) bool {
	// 将十六进制公钥字符串转换为字节数组
	// Ed25519公钥是32字节，比ECDSA公钥更紧凑
	publicKeyByte, _ := hex.DecodeString(pubKey)

	// 将十六进制消息字符串转换为字节数组
	// EdDSA可以直接验证任意长度的消息
	msgHashByte, _ := hex.DecodeString(msgHash)

	// 将十六进制签名字符串转换为字节数组
	// Ed25519签名是64字节，包含R点和s标量
	signature, _ := hex.DecodeString(sig)

	// 执行Ed25519签名验证
	// ed25519.Verify函数实现了完整的EdDSA验证算法
	// 返回true表示签名有效，false表示签名无效
	return ed25519.Verify(publicKeyByte, msgHashByte, signature)
}
