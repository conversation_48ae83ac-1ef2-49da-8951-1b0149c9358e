// Package rpc 提供钱包签名服务的gRPC接口实现
// 该包实现了区块链钱包的核心功能：密钥生成、公钥导出和消息签名
package rpc

import (
	"context"

	"github.com/ethereum/go-ethereum/log"
	"github.com/pkg/errors"

	"github.com/dapplink-labs/wallet-sign-go/leveldb"
	"github.com/dapplink-labs/wallet-sign-go/protobuf"
	"github.com/dapplink-labs/wallet-sign-go/protobuf/wallet"
	"github.com/dapplink-labs/wallet-sign-go/ssm"
)

// GetSupportSignWay 获取系统支持的签名算法列表
// 该方法返回当前钱包服务支持的所有数字签名算法类型
//
// 参数:
//   - ctx: 请求上下文，用于控制请求生命周期和传递元数据
//   - in: 支持签名方式查询请求，包含查询参数
//
// 返回值:
//   - *wallet.SupportSignWayResponse: 包含支持的签名算法列表的响应
//   - error: 错误信息，正常情况下为nil
//
// 支持的签名算法:
//   - ECDSA: 椭圆曲线数字签名算法，广泛用于比特币、以太坊等区块链
//   - EdDSA: Edwards曲线数字签名算法，具有更好的性能和安全性
func (s *RpcServer) GetSupportSignWay(ctx context.Context, in *wallet.SupportSignWayRequest) (*wallet.SupportSignWayResponse, error) {
	// 初始化支持的签名算法列表
	var signWay []*wallet.SignWay

	// 添加ECDSA签名算法支持
	// ECDSA是目前区块链领域最广泛使用的签名算法
	signWay = append(signWay, &wallet.SignWay{Schema: "ecdsa"})

	// 添加EdDSA签名算法支持
	// EdDSA提供更好的性能和安全性，适用于新一代区块链项目
	signWay = append(signWay, &wallet.SignWay{Schema: "eddsa"})

	// 返回成功响应，包含所有支持的签名算法
	return &wallet.SupportSignWayResponse{
		Code:    wallet.ReturnCode_SUCCESS,
		Msg:     "get sign way success",
		SignWay: signWay,
	}, nil
}

// ExportPublicKeyList 批量生成密钥对并导出公钥列表
// 该方法根据指定的签名算法类型和数量，批量生成密钥对，
// 将私钥安全存储到LevelDB中，并返回对应的公钥列表供客户端使用
//
// 参数:
//   - ctx: 请求上下文，用于控制请求生命周期
//   - in: 公钥导出请求，包含签名算法类型和生成数量
//
// 返回值:
//   - *wallet.ExportPublicKeyResponse: 包含生成的公钥列表的响应
//   - error: 错误信息，包括参数验证失败、密钥生成失败、存储失败等
//
// 安全注意事项:
//   - 私钥将被安全存储在本地LevelDB中，不会通过网络传输
//   - 支持批量生成，但限制单次最大生成数量为10000个，防止资源耗尽
//   - 每个密钥对都是独立生成的，确保密钥的随机性和唯一性
func (s *RpcServer) ExportPublicKeyList(ctx context.Context, in *wallet.ExportPublicKeyRequest) (*wallet.ExportPublicKeyResponse, error) {
	// 初始化响应对象，默认为错误状态
	resp := &wallet.ExportPublicKeyResponse{
		Code: wallet.ReturnCode_ERROR,
	}

	// 解析并验证签名算法类型
	cryptoType, err := protobuf.ParseTransactionType(in.Type)
	if err != nil {
		resp.Msg = "input type error"
		return resp, nil
	}

	// 验证生成数量限制，防止恶意请求导致资源耗尽
	// 单次最大生成10000个密钥对，这是一个合理的安全限制
	if in.Number > 10000 {
		resp.Msg = "Number must be less than 100000"
		return resp, nil
	}

	// 初始化密钥存储列表和返回的公钥列表
	var keyList []leveldb.Key      // 用于存储到数据库的密钥对
	var retKeyList []*wallet.PublicKey // 返回给客户端的公钥列表

	// 批量生成指定数量的密钥对
	for counter := 0; counter < int(in.Number); counter++ {
		var priKeyStr, pubKeyStr, compressPubkeyStr string
		var err error

		// 根据不同的签名算法类型生成相应的密钥对
		switch cryptoType {
		case protobuf.ECDSA:
			// 生成ECDSA密钥对，返回私钥、公钥和压缩公钥
			// ECDSA广泛用于比特币、以太坊等主流区块链
			priKeyStr, pubKeyStr, compressPubkeyStr, err = ssm.CreateECDSAKeyPair()
		case protobuf.EDDSA:
			// 生成EdDSA密钥对，EdDSA不需要压缩公钥概念
			// EdDSA具有更好的性能和安全性，适用于新一代区块链
			priKeyStr, pubKeyStr, err = ssm.CreateEdDSAKeyPair()
			compressPubkeyStr = pubKeyStr // EdDSA公钥本身就是压缩格式
		default:
			// 不支持的签名算法类型，返回错误
			return nil, errors.New("unsupported key type")
		}

		// 检查密钥生成是否成功
		if err != nil {
			log.Error("create key pair fail", "err", err)
			return nil, err
		}

		// 构造用于数据库存储的密钥对象
		keyItem := leveldb.Key{
			PrivateKey: priKeyStr, // 私钥将被安全存储，不对外暴露
			Pubkey:     pubKeyStr, // 公钥作为索引键使用
		}

		// 构造返回给客户端的公钥对象
		pukItem := &wallet.PublicKey{
			CompressPubkey: compressPubkeyStr, // 压缩格式公钥，节省存储空间
			Pubkey:         pubKeyStr,         // 完整格式公钥
		}

		// 添加到相应的列表中
		retKeyList = append(retKeyList, pukItem)
		keyList = append(keyList, keyItem)
	}

	// 批量存储所有生成的密钥对到LevelDB
	// 这是一个关键的安全操作，确保私钥被安全持久化
	isOk := s.db.StoreKeys(keyList)
	if !isOk {
		log.Error("store keys fail", "isOk", isOk)
		return nil, errors.New("store keys fail")
	}

	// 设置成功响应
	resp.Code = wallet.ReturnCode_SUCCESS
	resp.Msg = "create keys success"
	resp.PublicKey = retKeyList
	return resp, nil
}

// SignTxMessage 对交易消息进行数字签名
// 该方法是钱包服务的核心功能，根据提供的公钥查找对应的私钥，
// 使用指定的签名算法对消息哈希进行数字签名，确保交易的完整性和不可否认性
//
// 参数:
//   - ctx: 请求上下文，用于控制请求生命周期
//   - in: 签名请求，包含公钥、消息哈希和签名算法类型
//
// 返回值:
//   - *wallet.SignTxMessageResponse: 包含数字签名结果的响应
//   - error: 错误信息，包括参数验证失败、私钥查找失败、签名失败等
//
// 安全注意事项:
//   - 私钥永远不会离开服务器，确保密钥安全
//   - 支持多种签名算法，满足不同区块链的需求
//   - 消息哈希应该是经过安全哈希函数处理的固定长度数据
//   - 签名结果可以被任何人使用对应的公钥进行验证
func (s *RpcServer) SignTxMessage(ctx context.Context, in *wallet.SignTxMessageRequest) (*wallet.SignTxMessageResponse, error) {
	// 初始化响应对象，默认为错误状态
	resp := &wallet.SignTxMessageResponse{
		Code: wallet.ReturnCode_ERROR,
	}

	// 解析并验证签名算法类型
	cryptoType, err := protobuf.ParseTransactionType(in.Type)
	if err != nil {
		resp.Msg = "input type error"
		return resp, nil
	}

	// 根据公钥查找对应的私钥
	// 这是一个关键的安全操作，确保只有拥有私钥的服务才能进行签名
	privKey, isOk := s.db.GetPrivKey(in.PublicKey)
	if !isOk {
		return nil, errors.New("get private key by public key fail")
	}

	// 初始化签名结果变量
	var signature string
	var err2 error

	// 根据不同的签名算法类型执行相应的签名操作
	switch cryptoType {
	case protobuf.ECDSA:
		// 使用ECDSA算法对消息哈希进行签名
		// ECDSA签名广泛用于比特币、以太坊等区块链网络
		signature, err2 = ssm.SignECDSAMessage(privKey, in.MessageHash)
	case protobuf.EDDSA:
		// 使用EdDSA算法对消息哈希进行签名
		// EdDSA提供更好的性能和安全性，适用于新一代区块链
		signature, err2 = ssm.SignEdDSAMessage(privKey, in.MessageHash)
	default:
		// 不支持的签名算法类型，返回错误
		return nil, errors.New("unsupported key type")
	}

	// 检查签名操作是否成功
	if err2 != nil {
		return nil, err2
	}

	// 设置成功响应，返回数字签名结果
	resp.Msg = "sign tx message success"
	resp.Signature = signature // 十六进制编码的数字签名
	resp.Code = wallet.ReturnCode_SUCCESS
	return resp, nil
}
