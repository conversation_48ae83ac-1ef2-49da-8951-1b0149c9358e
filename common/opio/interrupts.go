package opio

import (
	"context"
	"os"
	"os/signal"
	"syscall"
)

var DefaultInterruptSignals = []os.Signal{
	os.Interrupt,
	os.Kill,
	syscall.SIGTERM,
	syscall.SIGQUIT,
}

func BlockOnInterruptsContext(ctx context.Context, signals ...os.Signal) {
	if len(signals) == 0 {
		signals = DefaultInterruptSignals
	}
	interruptChannel := make(chan os.Signal, 1)
	signal.Notify(interruptChannel, signals...)
	select {
	case <-interruptChannel:
	case <-ctx.Done():
		signal.Stop(interruptChannel)
	}
}

type interruptContextKeyType struct{}

var blockerContextKey = interruptContextKeyType{}

type interruptCatcher struct {
	incoming chan os.Signal
}

func (c *interruptCatcher) Block(ctx context.Context) {
	select {
	case <-c.incoming:
	case <-ctx.Done():
	}
}

type BlockFn func(ctx context.Context)

func WithInterruptBlocker(ctx context.Context) context.Context {
	if ctx.Value(blockerContextKey) != nil { // already has an interrupt handler
		return ctx
	}
	catcher := &interruptCatcher{
		incoming: make(chan os.Signal, 10),
	}
	signal.Notify(catcher.incoming, DefaultInterruptSignals...)

	return context.WithValue(ctx, blockerContextKey, BlockFn(catcher.Block))
}

//func WithBlocker(ctx context.Context, fn BlockFn) context.Context {
//	return context.WithValue(ctx, blockerContextKey, fn)
//}

//func BlockerFromContext(ctx context.Context) BlockFn {
//	v := ctx.Value(blockerContextKey)
//	if v == nil {
//		return nil
//	}
//	return v.(BlockFn)
//}

//func CancelOnInterrupt(ctx context.Context) context.Context {
//	inner, cancel := context.WithCancel(ctx)
//
//	blockOnInterrupt := BlockerFromContext(ctx)
//	if blockOnInterrupt == nil {
//		blockOnInterrupt = func(ctx context.Context) {
//			BlockOnInterruptsContext(ctx) // default signals
//		}
//	}
//
//	go func() {
//		blockOnInterrupt(ctx)
//		cancel()
//	}()
//
//	return inner
//}
