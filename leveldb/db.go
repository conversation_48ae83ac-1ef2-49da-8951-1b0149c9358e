// Package leveldb 提供LevelDB数据库的封装和工具函数
// 该包封装了LevelDB的基本操作，为密钥存储提供高性能的键值存储支持
package leveldb

import (
	"encoding/hex"
	"github.com/ethereum/go-ethereum/log"
	"github.com/syndtr/goleveldb/leveldb"
)

// LevelStore LevelDB存储引擎封装结构体
// 该结构体封装了LevelDB数据库实例，提供基本的键值存储操作
type LevelStore struct {
	*leveldb.DB // 嵌入LevelDB数据库实例
}

// NewLevelStore 创建新的LevelDB存储实例
// 该函数打开或创建LevelDB数据库文件，初始化存储引擎
//
// 参数:
//   - path: 数据库文件路径，LevelDB会在此路径创建数据库文件夹
//
// 返回值:
//   - *LevelStore: LevelDB存储引擎实例
//   - error: 数据库打开过程中的错误信息
//
// LevelDB特性:
//   - 高性能：基于LSM-Tree结构，写入性能优秀
//   - 持久化：数据自动持久化到磁盘
//   - 压缩：自动数据压缩，节省存储空间
//   - 原子性：单个操作具有原子性保证
//   - 有序：键按字典序自动排序
//
// 注意事项:
//   - 数据库文件夹一旦创建，不应该被其他程序修改
//   - 同一时间只能有一个进程打开数据库
//   - 建议定期备份数据库文件
func NewLevelStore(path string) (*LevelStore, error) {
	// 打开LevelDB数据库文件，使用默认选项
	// LevelDB会自动创建必要的文件和目录结构
	handle, err := leveldb.OpenFile(path, nil)
	if err != nil {
		log.Error("open level db file fail", "err", err)
		return nil, err
	}

	// 返回封装的LevelDB存储实例
	return &LevelStore{handle}, nil
}

// Put 向数据库中存储键值对
// 该方法将指定的键值对存储到LevelDB中，如果键已存在则覆盖原值
//
// 参数:
//   - key: 存储键的字节数组
//   - value: 存储值的字节数组
//
// 返回值:
//   - error: 存储操作的错误信息，成功时为nil
//
// 特性:
//   - 原子性：单个Put操作是原子的
//   - 持久化：数据会被持久化到磁盘
//   - 覆盖：如果键已存在，新值会覆盖旧值
func (db *LevelStore) Put(key []byte, value []byte) error {
	return db.DB.Put(key, value, nil)
}

// Get 从数据库中检索指定键的值
// 该方法根据提供的键从LevelDB中检索对应的值
//
// 参数:
//   - key: 要检索的键的字节数组
//
// 返回值:
//   - []byte: 对应的值的字节数组
//   - error: 检索操作的错误信息，键不存在时返回leveldb.ErrNotFound
//
// 特性:
//   - 高效：基于LSM-Tree的快速查找
//   - 一致性：读取操作具有一致性保证
func (db *LevelStore) Get(key []byte) ([]byte, error) {
	return db.DB.Get(key, nil)
}

// Delete 从数据库中删除指定键的记录
// 该方法从LevelDB中删除指定键及其对应的值
//
// 参数:
//   - key: 要删除的键的字节数组
//
// 返回值:
//   - error: 删除操作的错误信息，成功时为nil
//
// 特性:
//   - 原子性：单个Delete操作是原子的
//   - 幂等性：删除不存在的键不会产生错误
func (db *LevelStore) Delete(key []byte) error {
	return db.DB.Delete(key, nil)
}

// toBytes 将十六进制字符串转换为字节数组
// 该工具函数用于将十六进制编码的字符串转换为原始字节数据
//
// 参数:
//   - dataStr: 十六进制字符串（如"48656c6c6f"）
//
// 返回值:
//   - []byte: 对应的字节数组
//
// 注意：该函数忽略了解码错误，生产环境中应该处理错误
func toBytes(dataStr string) []byte {
	dataBytes, _ := hex.DecodeString(dataStr)
	return dataBytes
}

// toString 将字节数组转换为十六进制字符串
// 该工具函数用于将原始字节数据转换为十六进制编码的字符串
//
// 参数:
//   - byteArr: 原始字节数组
//
// 返回值:
//   - string: 对应的十六进制字符串（小写格式）
//
// 用途：
//   - 数据序列化：将二进制数据转换为可读格式
//   - 调试输出：便于查看和调试二进制数据
//   - 网络传输：十六进制字符串便于网络传输
func toString(byteArr []byte) string {
	return hex.EncodeToString(byteArr)
}
