// Package leveldb 提供基于LevelDB的密钥存储和管理功能
// 该包实现了安全的本地密钥存储，使用公钥作为索引来存储和检索对应的私钥
package leveldb

import "github.com/ethereum/go-ethereum/log"

// Keys 密钥存储管理器结构体
// 该结构体封装了LevelDB数据库操作，提供密钥的安全存储和检索功能
type Keys struct {
	db *LevelStore // 底层LevelDB存储引擎
}

// NewKeyStore 创建新的密钥存储实例
// 该函数初始化LevelDB数据库连接，为密钥存储提供持久化支持
//
// 参数:
//   - path: LevelDB数据库文件路径，用于存储密钥数据
//
// 返回值:
//   - *Keys: 密钥存储管理器实例
//   - error: 数据库初始化过程中的错误信息
//
// 安全注意事项:
//   - 数据库文件应该存储在安全的位置，具有适当的文件权限
//   - 建议对数据库文件进行加密保护
//   - 定期备份数据库文件以防止数据丢失
//   - 确保数据库路径具有足够的磁盘空间
func NewKeyStore(path string) (*Keys, error) {
	// 创建底层LevelDB存储实例
	db, err := NewLevelStore(path)
	if err != nil {
		log.Error("Could not create leveldb database.")
		return nil, err
	}

	// 返回密钥存储管理器实例
	return &Keys{
		db: db,
	}, nil
}

// GetPrivKey 根据公钥检索对应的私钥
// 该方法使用公钥作为索引，从LevelDB中安全地检索对应的私钥
//
// 参数:
//   - publicKey: 公钥的十六进制字符串表示，用作数据库索引
//
// 返回值:
//   - string: 对应的私钥十六进制字符串，如果未找到则返回"0x00"
//   - bool: 检索操作是否成功，true表示找到私钥，false表示未找到
//
// 安全注意事项:
//   - 私钥是敏感信息，调用方必须确保安全处理
//   - 该方法不会验证公钥格式的有效性
//   - 建议在调用后立即清除内存中的私钥副本
//   - 访问控制应该在更高层实现
func (k *Keys) GetPrivKey(publicKey string) (string, bool) {
	// 将公钥字符串转换为字节数组作为数据库键
	key := []byte(publicKey)

	// 从LevelDB中检索对应的私钥数据
	data, err := k.db.Get(key)
	if err != nil {
		// 未找到对应的私钥，返回默认值和失败状态
		return "0x00", false
	}

	// 将字节数据转换为十六进制字符串格式
	bstr := toString(data)
	return bstr, true
}

// StoreKeys 批量存储密钥对到数据库
// 该方法将多个密钥对批量存储到LevelDB中，使用公钥作为索引存储私钥
//
// 参数:
//   - keyList: 待存储的密钥对列表，每个元素包含公钥和私钥
//
// 返回值:
//   - bool: 存储操作是否全部成功，true表示所有密钥都成功存储
//
// 安全注意事项:
//   - 存储操作是原子性的，任何一个密钥存储失败都会导致整个操作失败
//   - 私钥在存储前会被转换为字节格式，增加一定的安全性
//   - 建议在存储前验证密钥对的有效性
//   - 存储失败时应该有适当的错误处理和回滚机制
func (k *Keys) StoreKeys(keyList []Key) bool {
	// 遍历所有待存储的密钥对
	for _, item := range keyList {
		// 使用公钥作为数据库键
		key := []byte(item.Pubkey)

		// 将私钥转换为字节格式作为数据库值
		value := toBytes(item.PrivateKey)

		// 执行数据库存储操作
		err := k.db.Put(key, value)
		if err != nil {
			// 记录存储失败的详细信息，但注意不要记录敏感的私钥内容
			log.Error("store key value fail", "err", err, "key", key, "value", value)
			return false
		}
	}

	// 所有密钥对都成功存储
	return true
}
