// Package hsm 提供硬件安全模块(Hardware Security Module)集成功能
// 该包实现了与Google Cloud KMS的集成，提供企业级的密钥管理和签名服务
// HSM确保密钥在硬件级别的安全性，满足高安全性要求的企业应用场景
package hsm

import (
	"context"
	"encoding/hex"
	"fmt"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/log"

	kms "cloud.google.com/go/kms/apiv1"
	"google.golang.org/api/option"
	kmspb "google.golang.org/genproto/googleapis/cloud/kms/v1"
)

// HsmClient Google Cloud KMS客户端结构体
// 该结构体封装了与Google Cloud KMS服务的交互，提供硬件级别的密钥管理和签名功能
type HsmClient struct {
	Ctx     context.Context              // 请求上下文，用于控制API调用的生命周期
	KeyName string                       // KMS密钥资源名称，格式：projects/*/locations/*/keyRings/*/cryptoKeys/*
	Gclient *kms.KeyManagementClient     // Google Cloud KMS客户端实例
}

// NewHSMClient 创建新的HSM客户端实例
// 该函数初始化与Google Cloud KMS的连接，为后续的密钥操作做准备
//
// 参数:
//   - ctx: 请求上下文，用于控制客户端生命周期和传递认证信息
//   - keyPath: Google Cloud服务账号密钥文件路径（JSON格式）
//   - keyName: KMS密钥的完整资源名称
//
// 返回值:
//   - *HsmClient: HSM客户端实例
//   - error: 客户端初始化过程中的错误信息
//
// HSM优势:
//   - 硬件级安全：密钥存储在专用硬件中，无法被软件提取
//   - 合规性：满足FIPS 140-2 Level 3等安全标准
//   - 高可用：Google Cloud提供99.95%的可用性保证
//   - 审计：所有密钥操作都有详细的审计日志
//   - 访问控制：基于IAM的细粒度权限控制
//
// 安全注意事项:
//   - 服务账号密钥文件必须妥善保管，具有适当的文件权限
//   - 建议使用最小权限原则配置IAM角色
//   - 定期轮换服务账号密钥
//   - 监控KMS API的使用情况和异常访问
func NewHSMClient(ctx context.Context, keyPath string, keyName string) (*HsmClient, error) {
	// 配置Google Cloud认证，使用服务账号密钥文件
	apikey := option.WithCredentialsFile(keyPath)

	// 创建KMS客户端实例，建立与Google Cloud KMS的连接
	client, err := kms.NewKeyManagementClient(ctx, apikey)
	if err != nil {
		log.Error("new key manager client fail", "err", err)
		return nil, err
	}

	// 返回配置完成的HSM客户端实例
	return &HsmClient{Ctx: ctx, KeyName: keyName, Gclient: client}, nil
}

// SignTransaction 使用HSM对交易哈希进行数字签名
// 该方法调用Google Cloud KMS的非对称签名API，使用存储在HSM中的私钥对交易哈希进行签名
//
// 参数:
//   - hash: 待签名的交易哈希，十六进制字符串格式
//
// 返回值:
//   - string: HSM生成的数字签名，十六进制字符串格式
//   - error: 签名过程中的错误信息，包括网络错误、权限错误等
//
// HSM签名流程:
//   1. 将十六进制哈希转换为字节数组
//   2. 构造KMS签名请求，指定使用SHA-256摘要
//   3. 调用KMS API执行硬件级签名操作
//   4. 返回签名结果的十六进制表示
//
// 安全优势:
//   - 私钥永远不离开HSM硬件，确保最高级别的安全性
//   - 所有签名操作都在专用硬件中执行，防止侧信道攻击
//   - 完整的审计日志记录所有签名操作
//   - 支持密钥轮换和版本管理
//
// 注意事项:
//   - 该方法忽略了哈希解码错误，生产环境中应该处理
//   - KMS API调用可能产生费用，建议监控使用量
//   - 网络延迟可能影响签名性能，适合对安全性要求高于性能的场景
func (hsm *HsmClient) SignTransaction(hash string) (string, error) {
	// 将十六进制哈希字符串转换为字节数组
	// 注意：这里忽略了解码错误，生产环境中应该处理
	hashByte, _ := hex.DecodeString(hash)

	// 构造KMS非对称签名请求
	req := kmspb.AsymmetricSignRequest{
		Name: hsm.KeyName, // 指定要使用的KMS密钥资源名称
		Digest: &kmspb.Digest{
			// 指定使用SHA-256摘要算法
			// 这确保了与区块链标准的兼容性
			Digest: &kmspb.Digest_Sha256{
				Sha256: hashByte[:],
			},
		},
	}

	// 调用Google Cloud KMS API执行签名操作
	// 签名在HSM硬件中安全执行，私钥永远不会暴露
	resp, err := hsm.Gclient.AsymmetricSign(hsm.Ctx, &req)
	if err != nil {
		// 签名失败，返回空哈希和错误信息
		return common.Hash{}.String(), err
	}

	// 将签名结果转换为十六进制字符串返回
	return hex.EncodeToString(resp.Signature), nil
}

// CreateKeyRing 在Google Cloud KMS中创建密钥环
// 密钥环是KMS中密钥的逻辑分组，用于组织和管理相关的密钥
//
// 参数:
//   - projectID: Google Cloud项目ID
//   - locationID: 密钥环的地理位置（如"global", "us-east1"等）
//   - keyRingID: 密钥环的唯一标识符
//
// 返回值:
//   - string: 创建成功的密钥环ID
//   - error: 创建过程中的错误信息
//
// 密钥环特性:
//   - 逻辑分组：将相关密钥组织在一起，便于管理
//   - 地理位置：可以指定密钥的存储位置，满足数据主权要求
//   - 访问控制：可以在密钥环级别设置IAM权限
//   - 不可删除：一旦创建，密钥环无法删除，只能禁用其中的密钥
//
// 最佳实践:
//   - 根据业务需求和安全要求选择合适的地理位置
//   - 使用有意义的命名约定，便于后续管理
//   - 考虑合规性要求，某些行业可能需要特定的地理位置
func (hsm *HsmClient) CreateKeyRing(projectID, locationID, keyRingID string) (string, error) {
	// 构造密钥环的父资源路径
	// 格式：projects/{project}/locations/{location}
	parent := fmt.Sprintf("projects/%s/locations/%s", projectID, locationID)

	// 调用KMS API创建密钥环
	_, err := hsm.Gclient.CreateKeyRing(hsm.Ctx, &kmspb.CreateKeyRingRequest{
		Parent:    parent,    // 父资源路径
		KeyRingId: keyRingID, // 密钥环的唯一标识符
	})
	if err != nil {
		log.Error("create key ring fail", "err", err)
		return "", err
	}

	// 返回创建成功的密钥环ID
	return keyRingID, nil
}

// CreateKeyPair 在Google Cloud KMS中创建加密密钥
// 该方法根据指定的签名算法在HSM中创建相应的密钥对
//
// 参数:
//   - projectID: Google Cloud项目ID
//   - locationID: 密钥的地理位置
//   - keyRingID: 密钥环ID，密钥将创建在此密钥环中
//   - keyID: 密钥的唯一标识符
//   - method: 签名算法类型（"ecdsa"或其他）
//
// 返回值:
//   - string: 创建成功的密钥资源名称
//   - error: 创建过程中的错误信息
//
// 支持的算法:
//   - ECDSA: 使用secp256k1曲线和SHA-256哈希，兼容比特币和以太坊
//   - RSA: 使用4096位RSA密钥和PKCS1填充，适用于传统应用
//
// HSM保护级别:
//   - 所有密钥都使用HSM保护级别，确保最高安全性
//   - 密钥生成和签名操作都在FIPS 140-2 Level 3认证的硬件中执行
//   - 私钥永远不会以明文形式离开HSM硬件
//
// 安全注意事项:
//   - 密钥一旦创建就无法删除，只能禁用
//   - 建议使用有意义的密钥ID，便于后续管理
//   - 考虑密钥轮换策略，定期创建新版本的密钥
func (hsm *HsmClient) CreateKeyPair(projectID, locationID, keyRingID, keyID, method string) (string, error) {
	// 构造密钥的父资源路径
	// 格式：projects/{project}/locations/{location}/keyRings/{keyRing}
	parent := fmt.Sprintf("projects/%s/locations/%s/keyRings/%s", projectID, locationID, keyRingID)

	// 根据签名算法类型配置密钥参数
	var key *kmspb.CryptoKey
	if method == "ecdsa" {
		// 配置ECDSA密钥参数
		key = &kmspb.CryptoKey{
			Purpose: kmspb.CryptoKey_ASYMMETRIC_SIGN, // 用途：非对称签名
			VersionTemplate: &kmspb.CryptoKeyVersionTemplate{
				// 使用secp256k1椭圆曲线和SHA-256哈希算法
				// 这与比特币和以太坊使用的标准一致
				Algorithm:       kmspb.CryptoKeyVersion_EC_SIGN_SECP256K1_SHA256,
				ProtectionLevel: kmspb.ProtectionLevel_HSM, // HSM硬件保护级别
			},
		}
	} else {
		// 配置RSA密钥参数（默认选项）
		key = &kmspb.CryptoKey{
			Purpose: kmspb.CryptoKey_ASYMMETRIC_SIGN, // 用途：非对称签名
			VersionTemplate: &kmspb.CryptoKeyVersionTemplate{
				// 使用4096位RSA密钥和PKCS1填充
				// 适用于需要RSA签名的传统应用
				Algorithm:       kmspb.CryptoKeyVersion_RSA_SIGN_RAW_PKCS1_4096,
				ProtectionLevel: kmspb.ProtectionLevel_HSM, // HSM硬件保护级别
			},
		}
	}

	// 调用KMS API创建加密密钥
	createdKey, err := hsm.Gclient.CreateCryptoKey(hsm.Ctx, &kmspb.CreateCryptoKeyRequest{
		Parent:      parent, // 父资源路径
		CryptoKeyId: keyID,  // 密钥的唯一标识符
		CryptoKey:   key,    // 密钥配置参数
	})
	if err != nil {
		log.Error("Failed to create ECDSA key: %v", err)
		return "", err
	}

	// 返回创建成功的密钥资源名称
	return createdKey.Name, nil
}
