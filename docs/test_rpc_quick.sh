#!/bin/bash

# wallet-sign-go RPC接口快速测试脚本
# 使用方法: ./test_rpc_quick.sh [host:port]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认服务地址
RPC_HOST=${1:-"127.0.0.1:8983"}

echo -e "${BLUE}=== wallet-sign-go RPC接口快速测试 ===${NC}"
echo -e "${YELLOW}服务地址: $RPC_HOST${NC}"

# 检查grpcurl是否安装
if ! command -v grpcurl &> /dev/null; then
    echo -e "${RED}错误: grpcurl未安装${NC}"
    echo "请先安装grpcurl:"
    echo "  macOS: brew install grpcurl"
    echo "  Linux: 下载并安装 https://github.com/fullstorydev/grpcurl/releases"
    exit 1
fi

# 检查jq是否安装
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}警告: jq未安装，输出将不会格式化${NC}"
    JQ_CMD="cat"
else
    JQ_CMD="jq ."
fi

# 测试函数
test_api() {
    local test_name="$1"
    local request="$2"
    local service="$3"
    
    echo -e "\n${GREEN}=== $test_name ===${NC}"
    echo -e "${YELLOW}请求:${NC} $request"
    echo -e "${YELLOW}响应:${NC}"
    
    if ! grpcurl -plaintext -d "$request" "$RPC_HOST" "$service" | $JQ_CMD; then
        echo -e "${RED}❌ 测试失败${NC}"
        return 1
    else
        echo -e "${GREEN}✅ 测试成功${NC}"
        return 0
    fi
}

# 1. 测试获取支持的签名算法
test_api "获取支持的签名算法" \
    '{}' \
    'dapplink.wallet.WalletService.getSupportSignWay'

# 2. 生成ECDSA密钥对
echo -e "\n${GREEN}=== 生成ECDSA密钥对 ===${NC}"
ECDSA_RESPONSE=$(grpcurl -plaintext -d '{
  "type": "ecdsa",
  "number": 1
}' "$RPC_HOST" dapplink.wallet.WalletService.exportPublicKeyList 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "$ECDSA_RESPONSE" | $JQ_CMD
    echo -e "${GREEN}✅ ECDSA密钥生成成功${NC}"
    
    # 提取公钥用于签名测试
    if command -v jq &> /dev/null; then
        ECDSA_PUBKEY=$(echo "$ECDSA_RESPONSE" | jq -r '.publicKey[0].pubkey' 2>/dev/null)
        if [ "$ECDSA_PUBKEY" != "null" ] && [ -n "$ECDSA_PUBKEY" ]; then
            echo -e "${YELLOW}提取的ECDSA公钥:${NC} $ECDSA_PUBKEY"
            
            # 3. 使用ECDSA进行签名测试
            test_api "ECDSA消息签名" \
                "{\"messageHash\": \"0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538\", \"publicKey\": \"$ECDSA_PUBKEY\", \"type\": \"ecdsa\"}" \
                'dapplink.wallet.WalletService.signTxMessage'
        fi
    fi
else
    echo -e "${RED}❌ ECDSA密钥生成失败${NC}"
fi

# 4. 生成EdDSA密钥对
echo -e "\n${GREEN}=== 生成EdDSA密钥对 ===${NC}"
EDDSA_RESPONSE=$(grpcurl -plaintext -d '{
  "type": "eddsa",
  "number": 1
}' "$RPC_HOST" dapplink.wallet.WalletService.exportPublicKeyList 2>/dev/null)

if [ $? -eq 0 ]; then
    echo "$EDDSA_RESPONSE" | $JQ_CMD
    echo -e "${GREEN}✅ EdDSA密钥生成成功${NC}"
    
    # 提取公钥用于签名测试
    if command -v jq &> /dev/null; then
        EDDSA_PUBKEY=$(echo "$EDDSA_RESPONSE" | jq -r '.publicKey[0].pubkey' 2>/dev/null)
        if [ "$EDDSA_PUBKEY" != "null" ] && [ -n "$EDDSA_PUBKEY" ]; then
            echo -e "${YELLOW}提取的EdDSA公钥:${NC} $EDDSA_PUBKEY"
            
            # 5. 使用EdDSA进行签名测试
            test_api "EdDSA消息签名" \
                "{\"messageHash\": \"0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538\", \"publicKey\": \"$EDDSA_PUBKEY\", \"type\": \"eddsa\"}" \
                'dapplink.wallet.WalletService.signTxMessage'
        fi
    fi
else
    echo -e "${RED}❌ EdDSA密钥生成失败${NC}"
fi

echo -e "\n${BLUE}=== 测试完成 ===${NC}"

# 提供一些有用的命令示例
echo -e "\n${YELLOW}=== 常用测试命令 ===${NC}"
echo "1. 手动测试获取支持的签名算法:"
echo "   grpcurl -plaintext -d '{}' $RPC_HOST dapplink.wallet.WalletService.getSupportSignWay"
echo ""
echo "2. 手动生成密钥对:"
echo "   grpcurl -plaintext -d '{\"type\": \"ecdsa\", \"number\": 1}' $RPC_HOST dapplink.wallet.WalletService.exportPublicKeyList"
echo ""
echo "3. 手动进行签名:"
echo "   grpcurl -plaintext -d '{\"messageHash\": \"0x123...\", \"publicKey\": \"04abc...\", \"type\": \"ecdsa\"}' $RPC_HOST dapplink.wallet.WalletService.signTxMessage"
echo ""
echo "4. 检查服务状态:"
echo "   netstat -tlnp | grep 8983"
echo "   ps aux | grep signature"
