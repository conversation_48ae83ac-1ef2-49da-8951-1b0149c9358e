# Protobuf字段命名规范分析

## 🔍 当前项目中的命名模式

### wallet-sign-go项目中的实际情况

通过分析`protobuf/wallet.proto`文件，发现了**不一致的命名模式**：

```protobuf
message SupportSignWayResponse {
  ReturnCode Code = 1;        // 大写开头
  string msg = 2;             // 小写开头
  repeated SignWay sign_way = 3; // 下划线分隔
}

message PublicKey {
    string compress_pubkey = 1; // 下划线分隔
    string pubkey = 2;          // 小写开头
}
```

### 生成的Go代码转换

protobuf编译器会自动转换字段名：

```go
// .proto文件中的字段 → Go结构体字段
type SupportSignWayResponse struct {
    Code    ReturnCode  // Code → Code (保持大写)
    Msg     string      // msg → Msg (转为大写)
    SignWay []*SignWay  // sign_way → SignWay (驼峰命名)
}

type PublicKey struct {
    CompressPubkey string // compress_pubkey → CompressPubkey
    Pubkey         string // pubkey → Pubkey
}
```

## 📋 Protobuf命名规范标准

### 1. Google官方风格指南

#### 字段命名规范
```protobuf
// ✅ 推荐：使用下划线分隔的小写字母
message UserProfile {
    string user_name = 1;
    string email_address = 2;
    int32 age_in_years = 3;
    bool is_active = 4;
}

// ❌ 不推荐：混合命名风格
message UserProfile {
    string UserName = 1;      // 大写开头
    string emailAddress = 2;  // 驼峰命名
    int32 age_in_years = 3;   // 下划线分隔
}
```

#### 消息名称规范
```protobuf
// ✅ 推荐：PascalCase (大写开头的驼峰命名)
message UserProfileRequest {
    // ...
}

message CreateAccountResponse {
    // ...
}
```

### 2. 不同语言的转换规则

| .proto字段 | Go | Java | Python | JavaScript |
|-----------|----|----- |--------|------------|
| `user_name` | `UserName` | `userName` | `user_name` | `userName` |
| `email_address` | `EmailAddress` | `emailAddress` | `email_address` | `emailAddress` |
| `is_active` | `IsActive` | `isActive` | `is_active` | `isActive` |

## 🎯 命名考虑因素

### 1. **跨语言兼容性**

#### 为什么使用下划线分隔？
```protobuf
// 原因1：自动转换为各语言的惯用命名
message ApiRequest {
    string api_key = 1;        // → Go: ApiKey, Java: apiKey
    string user_id = 2;        // → Go: UserId, Java: userId
    bool is_authenticated = 3; // → Go: IsAuthenticated, Java: isAuthenticated
}
```

#### 跨语言一致性示例
```
.proto:     user_name
Go:         UserName
Java:       userName  
Python:     user_name
C++:        user_name
JavaScript: userName
```

### 2. **JSON序列化考虑**

#### protobuf的JSON映射
```protobuf
message UserInfo {
    string user_name = 1;     // JSON: "userName" (驼峰)
    string email_addr = 2;    // JSON: "emailAddr" (驼峰)
}
```

#### 自定义JSON字段名
```protobuf
message UserInfo {
    string user_name = 1 [json_name = "user_name"];  // 强制JSON使用下划线
    string email_addr = 2 [json_name = "email"];     // 自定义JSON字段名
}
```

### 3. **向后兼容性**

#### 字段重命名的影响
```protobuf
// 版本1
message User {
    string name = 1;
}

// 版本2 - 重命名字段会破坏兼容性
message User {
    string user_name = 1;  // ❌ 这会破坏向后兼容性
}

// 正确的做法：添加新字段，保留旧字段
message User {
    string name = 1 [deprecated = true];
    string user_name = 2;
}
```

## 🔧 wallet-sign-go项目的问题分析

### 当前问题

1. **命名不一致**：
```protobuf
ReturnCode Code = 1;        // 大写开头
string msg = 2;             // 小写开头
repeated SignWay sign_way = 3; // 下划线分隔
```

2. **违反Google风格指南**：
```protobuf
// 当前 (不规范)
ReturnCode Code = 1;

// 应该是 (规范)
ReturnCode code = 1;
```

### 建议的修复方案

#### 方案1：完全遵循Google风格指南
```protobuf
message SupportSignWayResponse {
    ReturnCode code = 1;           // 改为小写
    string msg = 2;                // 保持小写
    repeated SignWay sign_way = 3; // 保持下划线分隔
}

message SignTxMessageRequest {
    string consumer_token = 1;
    string type = 2;
    string public_key = 3;
    string message_hash = 4;
}
```

#### 方案2：保持向后兼容，新字段遵循规范
```protobuf
message SupportSignWayResponse {
    ReturnCode Code = 1;           // 保持现状，避免破坏兼容性
    string msg = 2;
    repeated SignWay sign_way = 3;
}

// 新的message遵循规范
message NewApiRequest {
    string consumer_token = 1;
    string request_type = 2;
    int64 timestamp = 3;
}
```

## 💡 最佳实践建议

### 1. **新项目的命名规范**

```protobuf
syntax = "proto3";

// ✅ 消息名：PascalCase
message CreateWalletRequest {
    // ✅ 字段名：snake_case (下划线分隔的小写)
    string wallet_name = 1;
    string owner_address = 2;
    int32 security_level = 3;
    bool is_hardware_wallet = 4;
    repeated string supported_currencies = 5;
}

message CreateWalletResponse {
    ReturnCode result_code = 1;
    string error_message = 2;
    WalletInfo wallet_info = 3;
}
```

### 2. **字段命名的具体规则**

```protobuf
// ✅ 好的命名
message GoodExample {
    string user_id = 1;           // 清晰的标识符
    string email_address = 2;     // 完整的描述
    bool is_verified = 3;         // 布尔值用is_前缀
    int64 created_timestamp = 4;  // 明确的时间字段
    repeated string tag_list = 5; // 数组用_list后缀
}

// ❌ 避免的命名
message BadExample {
    string uid = 1;               // 缩写不清晰
    string email = 2;             // 可能歧义
    bool verified = 3;            // 布尔值不明确
    int64 created = 4;            // 时间字段不明确
    repeated string tags = 5;     // 数组命名不一致
}
```

### 3. **JSON兼容性考虑**

```protobuf
message ApiResponse {
    // 默认JSON字段名：resultCode
    ReturnCode result_code = 1;
    
    // 自定义JSON字段名
    string error_msg = 2 [json_name = "error"];
    
    // 保持下划线在JSON中
    string user_id = 3 [json_name = "user_id"];
}
```

## 🔄 迁移策略

### 对于wallet-sign-go项目

#### 短期策略（推荐）
1. **保持现有字段不变**，避免破坏向后兼容性
2. **新增字段严格遵循规范**
3. **在文档中说明命名规范**

#### 长期策略
1. **计划重大版本升级时统一命名**
2. **提供迁移指南**
3. **使用deprecated标记旧字段**

```protobuf
message SupportSignWayResponse {
    // 保持兼容性
    ReturnCode Code = 1 [deprecated = true];
    ReturnCode code = 4;  // 新的规范字段
    
    string msg = 2;
    repeated SignWay sign_way = 3;
}
```

## 📊 总结

### 核心原则

1. **一致性优于个人偏好**
2. **遵循语言生态的惯例**
3. **考虑跨语言兼容性**
4. **保持向后兼容性**

### 推荐做法

```protobuf
// ✅ 推荐的protobuf命名风格
syntax = "proto3";

message WalletSignRequest {
    string consumer_token = 1;
    string signature_type = 2;
    string public_key = 3;
    string message_hash = 4;
    int64 timestamp = 5;
    bool is_compressed = 6;
}

enum SignatureAlgorithm {
    SIGNATURE_ALGORITHM_UNSPECIFIED = 0;
    SIGNATURE_ALGORITHM_ECDSA = 1;
    SIGNATURE_ALGORITHM_EDDSA = 2;
}
```

对于wallet-sign-go项目，建议在不破坏现有API兼容性的前提下，逐步规范化命名，新增字段严格遵循Google protobuf风格指南。
