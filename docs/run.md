基于项目代码分析，以下是 `wallet-sign-go` 项目的详细运行指南：

## 1. 环境准备

**基础环境要求：**
- **Go版本**: Go 1.22.8 或更高版本
- **操作系统**: Linux/macOS/Windows
- **内存**: 至少 512MB
- **存储**: 至少 1GB 可用空间

**必需工具：**

````bash path=bin/compile.sh mode=EXCERPT
# Protocol Buffers 编译器
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# 需要安装 protoc
# Ubuntu/Debian: apt-get install protobuf-compiler
# macOS: brew install protobuf
# Windows: 下载 protoc 二进制文件
````

**可选工具：**
- Docker (容器化部署)
- Kubernetes + Helm (生产环境)
- golangci-lint (代码检查)

## 2. 编译构建

**克隆项目：**
```bash
git clone https://github.com/dapplink-labs/wallet-sign-go.git
cd wallet-sign-go
```

**编译 Protocol Buffers：**

````makefile path=Makefile mode=EXCERPT
proto:
	sh ./bin/compile.sh
````

```bash
# 生成 gRPC 代码
make proto
```

**编译主程序：**

````makefile path=Makefile mode=EXCERPT
signature:
	go mod tidy
	env GO111MODULE=on go build -v $(LDFLAGS) ./cmd/signature
````

```bash
# 下载依赖并编译
make signature

# 或者直接使用 go build
go mod tidy
go build -o signature ./cmd/signature
```

**验证编译结果：**
```bash
./signature version
```

## 3. 配置设置

**创建配置文件：**

````yaml path=config.yml mode=EDIT
# 服务器配置
rpc_server:
  host: "0.0.0.0"
  port: 8983

# LevelDB 数据库路径
leveldb_path: "./data"

# Google Cloud KMS 配置（可选）
credentials_file: ""
key_name: ""

# HSM 启用标志
hsm_enable: false
````

**环境变量设置：**
```bash
# 可选的环境变量
export GOPATH=$(go env GOPATH)
export GO111MODULE=on

# Google Cloud 凭证（如果使用 KMS）
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/credentials.json"
```

## 4. 启动方式

### 4.1 本地开发环境启动

**直接启动 RPC 服务：**

````go path=cmd/signature/cli.go mode=EXCERPT
{
	Name:        "rpc",
	Flags:       flags,
	Description: "Run rpc services",
	Action:      cliapp.LifecycleCmd(runRpc),
}
````

```bash
# 使用默认配置启动
./signature rpc

# 指定配置文件启动
./signature rpc --config config.yml

# 查看帮助信息
./signature rpc --help
```

### 4.2 Docker 容器启动

**构建 Docker 镜像：**

````dockerfile path=Dockerfile mode=EXCERPT
FROM golang:1.22.8-alpine3.18 as builder
RUN make

FROM alpine:3.18
COPY --from=builder /app/wallet-sign-go/wallet-sign-go /usr/local/bin
ENTRYPOINT ["wallet-sign-go"]
CMD ["-c", "/etc/wallet-sign-go/config.yml"]
````

```bash
# 构建镜像
docker build -t wallet-sign-go:latest .

# 运行容器
docker run -d \
  --name wallet-sign-go \
  -p 8983:8983 \
  -v $(pwd)/config.yml:/etc/wallet-sign-go/config.yml \
  -v $(pwd)/data:/app/data \
  wallet-sign-go:latest
```

### 4.3 生产环境部署

**使用 Helm 部署到 Kubernetes：**

````yaml path=.github/workflows/deploy-app.yaml mode=EXCERPT
- name: deploy app
  run: |
    registry="hkccr.ccs.tencentyun.com"
    helm upgrade --install ${{ env.app }} -f values.yaml charts
````

```bash
# 部署到 Kubernetes
helm upgrade --install wallet-sign-go \
  --set image.tag=v0.0.1 \
  --set config.rpcServer.port=8983 \
  ./charts

# 查看部署状态
kubectl get pods -l app=wallet-sign-go
kubectl logs -f deployment/wallet-sign-go
```

## 5. 验证测试

**检查服务状态：**
```bash
# 检查进程是否运行
ps aux | grep signature

# 检查端口是否监听
netstat -tlnp | grep 8983
# 或使用 ss
ss -tlnp | grep 8983
```

**测试 gRPC 接口：**

````bash path=test.md mode=EXCERPT
# 1. 测试支持的签名算法
grpcurl -plaintext -d '{
  "type": "ecdsa"
}' 127.0.0.1:8983 dapplink.wallet.WalletService.getSupportSignWay

# 2. 导出公钥列表
grpcurl -plaintext -d '{
  "type": "ecdsa",
  "number": "5"
}' 127.0.0.1:8983 dapplink.wallet.WalletService.exportPublicKeyList
````

**健康检查脚本：**

````bash path=health_check.sh mode=EDIT
#!/bin/bash
# 健康检查脚本

GRPC_HOST="127.0.0.1:8983"

# 检查 gRPC 服务是否响应
response=$(grpcurl -plaintext -d '{"type": "ecdsa"}' \
  $GRPC_HOST dapplink.wallet.WalletService.getSupportSignWay 2>/dev/null)

if echo "$response" | grep -q "Support this sign way"; then
    echo "✅ 服务运行正常"
    exit 0
else
    echo "❌ 服务异常"
    exit 1
fi
````

## 6. 常见问题

### 6.1 编译问题

**问题：protoc-gen-go 未找到**
```bash
# 解决方案：安装 Protocol Buffers 插件
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
export PATH=$PATH:$(go env GOPATH)/bin
```

**问题：依赖下载失败**
```bash
# 解决方案：配置 Go 代理
export GOPROXY=https://goproxy.cn,direct
go mod download
```

### 6.2 运行时问题

**问题：端口被占用**
```bash
# 查看占用进程
lsof -i :8983
# 或修改配置文件中的端口
```

**问题：LevelDB 权限错误**
```bash
# 确保数据目录权限正确
mkdir -p ./data
chmod 755 ./data
```

**问题：gRPC 连接失败**
```bash
# 检查防火墙设置
sudo ufw allow 8983
# 或检查服务绑定地址
netstat -tlnp | grep 8983
```

### 6.3 Docker 问题

**问题：容器启动失败**
```bash
# 查看容器日志
docker logs wallet-sign-go

# 检查配置文件挂载
docker run --rm -v $(pwd)/config.yml:/etc/wallet-sign-go/config.yml \
  wallet-sign-go:latest cat /etc/wallet-sign-go/config.yml
```

### 6.4 性能调优

**内存优化：**
```bash
# 设置 Go 运行时参数
export GOGC=100
export GOMEMLIMIT=512MiB
```

**并发调优：**
```bash
# 设置最大并发数
export GOMAXPROCS=4
```

通过以上步骤，您可以成功运行 `wallet-sign-go` 项目。建议在生产环境中使用 Docker 容器化部署，并配置适当的监控和日志收集。
