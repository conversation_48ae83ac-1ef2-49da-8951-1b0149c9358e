基于项目代码分析，这是一个企业级的钱包签名服务项目。以下是详细介绍：

## 1. 项目概述

`wallet-sign-go` 是一个基于Go语言开发的钱包签名服务，主要用于：
- 提供安全的数字签名服务
- 支持多种加密算法（ECDSA、EdDSA）
- 管理公私钥对
- 为区块链应用提供签名基础设施

## 2. 技术架构

**核心技术栈：**
- **语言**: Go 1.22.8
- **RPC框架**: gRPC (google.golang.org/grpc v1.67.1)
- **数据存储**: LevelDB (github.com/syndtr/goleveldb)
- **加密库**: Ethereum go-ethereum v1.14.11
- **容器化**: Docker + Alpine Linux
- **编排**: Kubernetes + Helm
- **云服务**: Google Cloud KMS集成

````go path=go.mod mode=EXCERPT
module github.com/dapplink-labs/wallet-sign-go

go 1.22.8

require (
	cloud.google.com/go/kms v1.20.1
	github.com/ethereum/go-ethereum v1.14.11
	google.golang.org/grpc v1.67.1
	...
)
````

## 3. 核心功能

**支持的签名算法：**
- **ECDSA**: 椭圆曲线数字签名算法
- **EdDSA**: Edwards曲线数字签名算法

**主要特性：**
- 密钥生成和管理
- 消息哈希签名
- 公钥导出
- HSM（硬件安全模块）集成
- LevelDB本地密钥存储

````go path=services/rpc/handle.go mode=EXCERPT
func (s *RpcServer) GetSupportSignWay(ctx context.Context, in *wallet.SupportSignWayRequest) (*wallet.SupportSignWayResponse, error) {
	var signWay []*wallet.SignWay
	signWay = append(signWay, &wallet.SignWay{Schema: "ecdsa"})
	signWay = append(signWay, &wallet.SignWay{Schema: "eddsa"})
	...
}
````

## 4. 项目结构

```
wallet-sign-go/
├── cmd/signature/          # 主程序入口
├── protobuf/              # gRPC协议定义
├── services/rpc/          # RPC服务实现
├── ssm/                   # 签名算法实现
├── leveldb/               # 数据库操作
├── hsm/                   # HSM集成
├── config/                # 配置管理
├── common/                # 通用工具
├── bin/                   # 编译脚本
└── charts/                # Helm部署配置
```

**关键文件：**
- `cmd/signature/main.go`: 应用程序入口
- `protobuf/wallet.proto`: gRPC服务定义
- `services/rpc/handle.go`: 业务逻辑实现
- `ssm/ecdsa.go`, `ssm/eddsa.go`: 签名算法实现

## 5. 部署方式

**Docker容器化：**

````dockerfile path=Dockerfile mode=EXCERPT
FROM golang:1.22.8-alpine3.18 as builder
# 构建阶段
COPY . /app/wallet-sign-go
RUN make

FROM alpine:3.18
# 运行阶段
COPY --from=builder /app/wallet-sign-go/wallet-sign-go /usr/local/bin
ENTRYPOINT ["wallet-sign-go"]
CMD ["-c", "/etc/wallet-sign-go/config.yml"]
````

**CI/CD流程：**
- GitHub Actions自动化构建
- 腾讯云容器镜像仓库
- Helm自动部署到Kubernetes

````yaml path=.github/workflows/deploy-app.yaml mode=EXCERPT
- name: deploy app
  run: |
    registry="hkccr.ccs.tencentyun.com"
    docker build --pull -t ${tag_name}:v0.0.1 .
    helm upgrade --install ${{ env.app }} -f values.yaml charts
````

## 6. API接口

**gRPC服务接口：**

````protobuf path=protobuf/wallet.proto mode=EXCERPT
service WalletService {
  rpc getSupportSignWay(SupportSignWayRequest) returns (SupportSignWayResponse) {}
  rpc exportPublicKeyList(ExportPublicKeyRequest) returns (ExportPublicKeyResponse) {}
  rpc signTxMessage(SignTxMessageRequest) returns (SignTxMessageResponse) {}
}
````

**主要接口功能：**
1. **getSupportSignWay**: 查询支持的签名算法
2. **exportPublicKeyList**: 导出公钥列表
3. **signTxMessage**: 对消息进行数字签名

**使用示例：**

````bash path=test.md mode=EXCERPT
# 消息签名
grpcurl -plaintext -d '{
  "messageHash": "0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538",
  "publicKey": "04c2fb70e8bb957e5be63b05dd35be4e62ba0e84330fc82794292ff58447462a53b52d60fe0a78f1218117279830b558245cf7c1f5e82735d2680b394addc7117c",
  "type": "ecdsa"
}' 127.0.0.1:8983 dapplink.wallet.WalletService.signTxMessage
````

## 7. 配置说明

**主要配置项：**

````go path=config/config.go mode=EXCERPT
type Config struct {
	LevelDbPath     string    // LevelDB数据库路径
	RpcServer       ServerConfig // gRPC服务器配置
	CredentialsFile string    // 凭证文件路径
	KeyName         string    // 密钥名称
	HsmEnable       bool      // HSM启用标志
}
````

**启动命令：**

````makefile path=Makefile mode=EXCERPT
signature:
	go mod tidy
	env GO111MODULE=on go build -v $(LDFLAGS) ./cmd/signature
````

**运行方式：**
```bash
# 编译
make signature

# 启动RPC服务
./signature rpc --config config.yml
```

这个项目为区块链应用提供了安全、可靠的签名服务基础设施，支持企业级部署和HSM集成，是一个完整的钱包签名解决方案。
