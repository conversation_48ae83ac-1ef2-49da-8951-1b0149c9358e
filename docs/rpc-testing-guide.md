# wallet-sign-go RPC接口测试指南

## 前置准备

### 1. 安装必要工具

#### 安装 grpcurl
```bash
# macOS
brew install grpcurl

# Linux
# 下载最新版本
wget https://github.com/fullstorydev/grpcurl/releases/download/v1.8.9/grpcurl_1.8.9_linux_x86_64.tar.gz
tar -xzf grpcurl_1.8.9_linux_x86_64.tar.gz
sudo mv grpcurl /usr/local/bin/

# 验证安装
grpcurl --version
```

#### 安装 jq (用于格式化JSON输出)
```bash
# macOS
brew install jq

# Linux
sudo apt-get install jq
# 或
sudo yum install jq
```

### 2. 启动服务

#### 编译项目
```bash
# 进入项目目录
cd /path/to/wallet-sign-go

# 编译
make signature
```

#### 启动RPC服务
```bash
# 使用默认配置启动
./signature rpc

# 或指定配置文件
./signature rpc --config config.yml

# 或使用命令行参数
./signature rpc \
  --rpc.host 0.0.0.0 \
  --rpc.port 8983 \
  --leveldb.path ./data \
  --hsm.enable false
```

#### 验证服务启动
```bash
# 检查端口是否监听
netstat -tlnp | grep 8983
# 或
ss -tlnp | grep 8983

# 检查进程
ps aux | grep signature
```

## RPC接口测试

### 1. 获取支持的签名算法

#### 基本请求
```bash
grpcurl -plaintext -d '{}' 127.0.0.1:8983 dapplink.wallet.WalletService.getSupportSignWay
```

#### 格式化输出
```bash
grpcurl -plaintext -d '{}' 127.0.0.1:8983 dapplink.wallet.WalletService.getSupportSignWay | jq .
```

#### 预期响应
```json
{
  "code": "SUCCESS",
  "msg": "get sign way success",
  "signWay": [
    {
      "schema": "ecdsa"
    },
    {
      "schema": "eddsa"
    }
  ]
}
```

### 2. 生成密钥对并导出公钥

#### ECDSA密钥生成
```bash
# 生成1个ECDSA密钥对
grpcurl -plaintext -d '{
  "type": "ecdsa",
  "number": 1
}' 127.0.0.1:8983 dapplink.wallet.WalletService.exportPublicKeyList | jq .
```

#### EdDSA密钥生成
```bash
# 生成1个EdDSA密钥对
grpcurl -plaintext -d '{
  "type": "eddsa",
  "number": 1
}' 127.0.0.1:8983 dapplink.wallet.WalletService.exportPublicKeyList | jq .
```

#### 批量生成密钥
```bash
# 生成5个ECDSA密钥对
grpcurl -plaintext -d '{
  "type": "ecdsa",
  "number": 5
}' 127.0.0.1:8983 dapplink.wallet.WalletService.exportPublicKeyList | jq .
```

#### 预期响应格式
```json
{
  "code": "SUCCESS",
  "msg": "create keys success",
  "publicKey": [
    {
      "compressPubkey": "02c2fb70e8bb957e5be63b05dd35be4e62ba0e84330fc82794292ff58447462a53",
      "pubkey": "04c2fb70e8bb957e5be63b05dd35be4e62ba0e84330fc82794292ff58447462a53b52d60fe0a78f1218117279830b558245cf7c1f5e82735d2680b394addc7117c"
    }
  ]
}
```

### 3. 消息签名

#### ECDSA消息签名
```bash
# 使用之前生成的公钥进行签名
grpcurl -plaintext -d '{
  "messageHash": "0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538",
  "publicKey": "04c2fb70e8bb957e5be63b05dd35be4e62ba0e84330fc82794292ff58447462a53b52d60fe0a78f1218117279830b558245cf7c1f5e82735d2680b394addc7117c",
  "type": "ecdsa"
}' 127.0.0.1:8983 dapplink.wallet.WalletService.signTxMessage | jq .
```

#### EdDSA消息签名
```bash
# 使用EdDSA公钥进行签名
grpcurl -plaintext -d '{
  "messageHash": "0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538",
  "publicKey": "YOUR_EDDSA_PUBLIC_KEY_HERE",
  "type": "eddsa"
}' 127.0.0.1:8983 dapplink.wallet.WalletService.signTxMessage | jq .
```

#### 预期响应格式
```json
{
  "code": "SUCCESS",
  "msg": "sign tx message success",
  "signature": "b3cf4df645d385e57c1498ae64cb32f2caf64546bfcb894b70e7dfc454ede9aa32849ffbac8b6605b2febacf8b605f2b688f899a09eb355e8f540aed1125897f00"
}
```

## 完整测试流程脚本

### 创建测试脚本
```bash
cat > test_wallet_rpc.sh << 'EOF'
#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 服务地址
RPC_HOST="127.0.0.1:8983"

echo -e "${YELLOW}=== wallet-sign-go RPC接口测试 ===${NC}"

# 1. 测试获取支持的签名算法
echo -e "\n${GREEN}1. 测试获取支持的签名算法${NC}"
grpcurl -plaintext -d '{}' $RPC_HOST dapplink.wallet.WalletService.getSupportSignWay | jq .

# 2. 生成ECDSA密钥对
echo -e "\n${GREEN}2. 生成ECDSA密钥对${NC}"
ECDSA_RESPONSE=$(grpcurl -plaintext -d '{
  "type": "ecdsa",
  "number": 1
}' $RPC_HOST dapplink.wallet.WalletService.exportPublicKeyList)

echo "$ECDSA_RESPONSE" | jq .

# 提取公钥用于签名测试
ECDSA_PUBKEY=$(echo "$ECDSA_RESPONSE" | jq -r '.publicKey[0].pubkey')
echo -e "${YELLOW}提取的ECDSA公钥: $ECDSA_PUBKEY${NC}"

# 3. 使用ECDSA公钥进行签名
echo -e "\n${GREEN}3. 使用ECDSA进行消息签名${NC}"
grpcurl -plaintext -d "{
  \"messageHash\": \"0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538\",
  \"publicKey\": \"$ECDSA_PUBKEY\",
  \"type\": \"ecdsa\"
}" $RPC_HOST dapplink.wallet.WalletService.signTxMessage | jq .

# 4. 生成EdDSA密钥对
echo -e "\n${GREEN}4. 生成EdDSA密钥对${NC}"
EDDSA_RESPONSE=$(grpcurl -plaintext -d '{
  "type": "eddsa",
  "number": 1
}' $RPC_HOST dapplink.wallet.WalletService.exportPublicKeyList)

echo "$EDDSA_RESPONSE" | jq .

# 提取公钥用于签名测试
EDDSA_PUBKEY=$(echo "$EDDSA_RESPONSE" | jq -r '.publicKey[0].pubkey')
echo -e "${YELLOW}提取的EdDSA公钥: $EDDSA_PUBKEY${NC}"

# 5. 使用EdDSA公钥进行签名
echo -e "\n${GREEN}5. 使用EdDSA进行消息签名${NC}"
grpcurl -plaintext -d "{
  \"messageHash\": \"0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538\",
  \"publicKey\": \"$EDDSA_PUBKEY\",
  \"type\": \"eddsa\"
}" $RPC_HOST dapplink.wallet.WalletService.signTxMessage | jq .

echo -e "\n${GREEN}=== 测试完成 ===${NC}"
EOF

chmod +x test_wallet_rpc.sh
```

### 运行测试脚本
```bash
./test_wallet_rpc.sh
```

## 错误处理和调试

### 常见错误及解决方案

#### 1. 连接失败
```bash
# 错误信息: failed to dial target host "127.0.0.1:8983": connect: connection refused

# 解决方案:
# 1. 检查服务是否启动
ps aux | grep signature

# 2. 检查端口是否正确
netstat -tlnp | grep 8983

# 3. 检查防火墙设置
sudo ufw status
```

#### 2. 签名失败
```bash
# 错误信息: get private key by public key fail

# 解决方案:
# 1. 确保使用的是刚生成的公钥
# 2. 检查数据库是否正常工作
# 3. 确认公钥格式正确
```

#### 3. 参数错误
```bash
# 错误信息: input type error

# 解决方案:
# 1. 检查type参数是否为"ecdsa"或"eddsa"
# 2. 确认JSON格式正确
# 3. 检查字段名称拼写
```

### 调试技巧

#### 1. 查看服务日志
```bash
# 如果服务在前台运行，直接查看输出
# 如果服务在后台运行，查看日志文件
tail -f /path/to/logfile
```

#### 2. 使用详细输出
```bash
# grpcurl添加详细输出
grpcurl -plaintext -v -d '{}' 127.0.0.1:8983 dapplink.wallet.WalletService.getSupportSignWay
```

#### 3. 检查数据库状态
```bash
# 检查LevelDB数据目录
ls -la ./data/
```

## 性能测试

### 批量密钥生成测试
```bash
# 测试生成大量密钥的性能
time grpcurl -plaintext -d '{
  "type": "ecdsa",
  "number": 100
}' 127.0.0.1:8983 dapplink.wallet.WalletService.exportPublicKeyList > /dev/null
```

### 并发签名测试
```bash
# 使用并发测试签名性能
for i in {1..10}; do
  grpcurl -plaintext -d '{
    "messageHash": "0x9ca77bd43a45da2399da96159b554bebdd89839eec73a8ff0626abfb2fb4b538",
    "publicKey": "YOUR_PUBLIC_KEY",
    "type": "ecdsa"
  }' 127.0.0.1:8983 dapplink.wallet.WalletService.signTxMessage &
done
wait
```

这个测试指南提供了完整的RPC接口测试方法，包括工具安装、服务启动、接口测试、错误处理和性能测试等各个方面。
