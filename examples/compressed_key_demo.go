package main

import (
	"encoding/hex"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/crypto"
)

// CompressedKeyDemo 演示压缩公钥的生成、压缩、解压缩过程
func main() {
	fmt.Println("=== 压缩公钥技术演示 ===\n")

	// 1. 生成椭圆曲线密钥对
	fmt.Println("1. 生成 secp256k1 密钥对")
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		panic(err)
	}

	// 2. 提取私钥和公钥信息
	fmt.Println("\n2. 密钥信息分析")
	
	// 私钥 (32字节标量)
	privateKeyBytes := crypto.FromECDSA(privateKey)
	fmt.Printf("私钥 (32字节): %s\n", hex.EncodeToString(privateKeyBytes))
	
	// 公钥坐标
	x := privateKey.PublicKey.X
	y := privateKey.PublicKey.Y
	fmt.Printf("公钥 X坐标: %064x\n", x)
	fmt.Printf("公钥 Y坐标: %064x\n", y)
	fmt.Printf("Y坐标奇偶性: %s\n", getParityString(y))

	// 3. 生成不同格式的公钥
	fmt.Println("\n3. 公钥格式对比")
	
	// 未压缩公钥 (65字节)
	uncompressedPubKey := crypto.FromECDSAPub(&privateKey.PublicKey)
	fmt.Printf("未压缩公钥 (65字节): %s\n", hex.EncodeToString(uncompressedPubKey))
	fmt.Printf("  - 前缀: %02x (表示未压缩)\n", uncompressedPubKey[0])
	fmt.Printf("  - X坐标: %s\n", hex.EncodeToString(uncompressedPubKey[1:33]))
	fmt.Printf("  - Y坐标: %s\n", hex.EncodeToString(uncompressedPubKey[33:65]))
	
	// 压缩公钥 (33字节)
	compressedPubKey := crypto.CompressPubkey(&privateKey.PublicKey)
	fmt.Printf("\n压缩公钥 (33字节): %s\n", hex.EncodeToString(compressedPubKey))
	fmt.Printf("  - 前缀: %02x (表示Y坐标%s)\n", compressedPubKey[0], getPrefixMeaning(compressedPubKey[0]))
	fmt.Printf("  - X坐标: %s\n", hex.EncodeToString(compressedPubKey[1:33]))

	// 4. 空间节省分析
	fmt.Println("\n4. 存储空间分析")
	fmt.Printf("未压缩公钥大小: %d 字节 (%d 十六进制字符)\n", len(uncompressedPubKey), len(hex.EncodeToString(uncompressedPubKey)))
	fmt.Printf("压缩公钥大小: %d 字节 (%d 十六进制字符)\n", len(compressedPubKey), len(hex.EncodeToString(compressedPubKey)))
	spaceSaving := float64(len(uncompressedPubKey)-len(compressedPubKey)) / float64(len(uncompressedPubKey)) * 100
	fmt.Printf("空间节省: %d 字节 (%.1f%%)\n", len(uncompressedPubKey)-len(compressedPubKey), spaceSaving)

	// 5. 演示压缩公钥解压缩过程
	fmt.Println("\n5. 压缩公钥解压缩演示")
	decompressedPubKey, err := decompressPublicKey(compressedPubKey)
	if err != nil {
		panic(err)
	}
	
	fmt.Printf("解压缩后公钥: %s\n", hex.EncodeToString(decompressedPubKey))
	fmt.Printf("与原始公钥一致: %t\n", hex.EncodeToString(decompressedPubKey) == hex.EncodeToString(uncompressedPubKey))

	// 6. 签名验证测试
	fmt.Println("\n6. 签名验证测试")
	message := "Hello, Blockchain!"
	messageHash := crypto.Keccak256Hash([]byte(message))
	
	// 使用私钥签名
	signature, err := crypto.Sign(messageHash.Bytes(), privateKey)
	if err != nil {
		panic(err)
	}
	
	fmt.Printf("消息: %s\n", message)
	fmt.Printf("消息哈希: %s\n", messageHash.Hex())
	fmt.Printf("签名: %s\n", hex.EncodeToString(signature))
	
	// 使用未压缩公钥验证
	uncompressedValid := crypto.VerifySignature(uncompressedPubKey, messageHash.Bytes(), signature[:64])
	fmt.Printf("未压缩公钥验证结果: %t\n", uncompressedValid)
	
	// 使用压缩公钥验证 (需要先解压缩)
	compressedValid := crypto.VerifySignature(decompressedPubKey, messageHash.Bytes(), signature[:64])
	fmt.Printf("压缩公钥验证结果: %t\n", compressedValid)

	// 7. 区块链应用场景模拟
	fmt.Println("\n7. 区块链应用场景模拟")
	simulateBlockchainUsage(compressedPubKey, uncompressedPubKey)
}

// getParityString 获取大整数的奇偶性描述
func getParityString(num *big.Int) string {
	if num.Bit(0) == 0 {
		return "偶数"
	}
	return "奇数"
}

// getPrefixMeaning 解释压缩公钥前缀的含义
func getPrefixMeaning(prefix byte) string {
	switch prefix {
	case 0x02:
		return "Y坐标为偶数"
	case 0x03:
		return "Y坐标为奇数"
	default:
		return "未知前缀"
	}
}

// decompressPublicKey 演示压缩公钥的解压缩过程
func decompressPublicKey(compressedPubKey []byte) ([]byte, error) {
	if len(compressedPubKey) != 33 {
		return nil, fmt.Errorf("invalid compressed public key length")
	}

	// 解析前缀和X坐标
	prefix := compressedPubKey[0]
	if prefix != 0x02 && prefix != 0x03 {
		return nil, fmt.Errorf("invalid compressed public key prefix")
	}

	// 提取X坐标
	x := new(big.Int).SetBytes(compressedPubKey[1:33])

	// secp256k1 曲线参数
	p, _ := new(big.Int).SetString("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F", 16)

	// 计算 y² = x³ + 7 (mod p)
	x3 := new(big.Int).Mul(x, x)
	x3.Mul(x3, x)
	x3.Add(x3, big.NewInt(7))
	x3.Mod(x3, p)

	// 计算 y = √(x³ + 7) (mod p)
	y := new(big.Int).ModSqrt(x3, p)
	if y == nil {
		return nil, fmt.Errorf("invalid point: no square root exists")
	}

	// 根据前缀选择正确的y值
	if (prefix == 0x02 && y.Bit(0) == 1) || (prefix == 0x03 && y.Bit(0) == 0) {
		y.Sub(p, y)
	}

	// 构造未压缩公钥格式
	uncompressed := make([]byte, 65)
	uncompressed[0] = 0x04
	copy(uncompressed[1:33], x.Bytes())
	copy(uncompressed[33:65], y.Bytes())

	return uncompressed, nil
}

// simulateBlockchainUsage 模拟区块链中的使用场景
func simulateBlockchainUsage(compressedPubKey, uncompressedPubKey []byte) {
	// 模拟比特币交易
	fmt.Println("\n比特币交易场景:")
	btcTxSize := calculateBitcoinTxSize(compressedPubKey, uncompressedPubKey)
	fmt.Printf("  使用压缩公钥的交易大小: %d 字节\n", btcTxSize.compressed)
	fmt.Printf("  使用未压缩公钥的交易大小: %d 字节\n", btcTxSize.uncompressed)
	fmt.Printf("  交易大小节省: %d 字节\n", btcTxSize.uncompressed-btcTxSize.compressed)
	
	// 模拟以太坊智能合约
	fmt.Println("\n以太坊智能合约场景:")
	ethGasCost := calculateEthereumGasCost(compressedPubKey, uncompressedPubKey)
	fmt.Printf("  存储压缩公钥的Gas成本: %d\n", ethGasCost.compressed)
	fmt.Printf("  存储未压缩公钥的Gas成本: %d\n", ethGasCost.uncompressed)
	fmt.Printf("  Gas成本节省: %d (%.1f%%)\n", 
		ethGasCost.uncompressed-ethGasCost.compressed,
		float64(ethGasCost.uncompressed-ethGasCost.compressed)/float64(ethGasCost.uncompressed)*100)
}

type TxSize struct {
	compressed   int
	uncompressed int
}

type GasCost struct {
	compressed   int
	uncompressed int
}

// calculateBitcoinTxSize 计算比特币交易大小
func calculateBitcoinTxSize(compressedPubKey, uncompressedPubKey []byte) TxSize {
	// 简化的比特币交易大小计算
	baseTxSize := 180 // 基础交易大小 (输入输出等)
	
	return TxSize{
		compressed:   baseTxSize + len(compressedPubKey),
		uncompressed: baseTxSize + len(uncompressedPubKey),
	}
}

// calculateEthereumGasCost 计算以太坊存储Gas成本
func calculateEthereumGasCost(compressedPubKey, uncompressedPubKey []byte) GasCost {
	// 以太坊存储成本: 每字节约625 gas
	gasPerByte := 625
	
	return GasCost{
		compressed:   len(compressedPubKey) * gasPerByte,
		uncompressed: len(uncompressedPubKey) * gasPerByte,
	}
}
