package main

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/crypto"
)

// CurveType 椭圆曲线类型枚举
type CurveType string

const (
	SECP256K1 CurveType = "secp256k1"
	SECP256R1 CurveType = "secp256r1" // NIST P-256
	SECP384R1 CurveType = "secp384r1" // NIST P-384
	SECP521R1 CurveType = "secp521r1" // NIST P-521
)

// CurveInfo 椭圆曲线信息
type CurveInfo struct {
	Name          string
	KeySize       int    // 私钥长度(位)
	SecurityLevel int    // 安全强度(位)
	OID           string // 对象标识符
	Description   string
}

// EllipticCurve 椭圆曲线接口
type EllipticCurve interface {
	GenerateKeyPair() (privateKey, publicKey, compressedPublicKey string, err error)
	Sign(privateKey, messageHash string) (signature string, err error)
	Verify(publicKey, messageHash, signature string) (bool, error)
	GetCurveInfo() CurveInfo
}

// Secp256k1Curve secp256k1曲线实现 (当前wallet-sign-go使用的)
type Secp256k1Curve struct{}

func (c *Secp256k1Curve) GenerateKeyPair() (string, string, string, error) {
	// 使用go-ethereum的实现
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", "", err
	}

	priKeyStr := hex.EncodeToString(crypto.FromECDSA(privateKey))
	pubKeyStr := hex.EncodeToString(crypto.FromECDSAPub(&privateKey.PublicKey))
	compressPubkeyStr := hex.EncodeToString(crypto.CompressPubkey(&privateKey.PublicKey))

	return priKeyStr, pubKeyStr, compressPubkeyStr, nil
}

func (c *Secp256k1Curve) Sign(privateKey, messageHash string) (string, error) {
	privByte, err := hex.DecodeString(privateKey)
	if err != nil {
		return "", err
	}

	privKeyEcdsa, err := crypto.ToECDSA(privByte)
	if err != nil {
		return "", err
	}

	hashByte, err := hex.DecodeString(messageHash)
	if err != nil {
		return "", err
	}

	signatureByte, err := crypto.Sign(hashByte, privKeyEcdsa)
	if err != nil {
		return "", err
	}

	return hex.EncodeToString(signatureByte), nil
}

func (c *Secp256k1Curve) Verify(publicKey, messageHash, signature string) (bool, error) {
	pubKeyBytes, err := hex.DecodeString(publicKey)
	if err != nil {
		return false, err
	}

	hashBytes, err := hex.DecodeString(messageHash)
	if err != nil {
		return false, err
	}

	sigBytes, err := hex.DecodeString(signature)
	if err != nil {
		return false, err
	}

	return crypto.VerifySignature(pubKeyBytes, hashBytes, sigBytes[:64]), nil
}

func (c *Secp256k1Curve) GetCurveInfo() CurveInfo {
	return CurveInfo{
		Name:          "secp256k1",
		KeySize:       256,
		SecurityLevel: 128,
		OID:           "*********.10",
		Description:   "Bitcoin/Ethereum标准曲线，Koblitz曲线，性能优异",
	}
}

// Secp256r1Curve NIST P-256曲线实现
type Secp256r1Curve struct{}

func (c *Secp256r1Curve) GenerateKeyPair() (string, string, string, error) {
	privateKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		return "", "", "", err
	}

	// 私钥转换
	privKeyBytes := privateKey.D.Bytes()
	// 确保私钥是32字节
	if len(privKeyBytes) < 32 {
		padded := make([]byte, 32)
		copy(padded[32-len(privKeyBytes):], privKeyBytes)
		privKeyBytes = padded
	}

	// 公钥转换 (未压缩格式)
	pubKeyBytes := elliptic.Marshal(elliptic.P256(), privateKey.X, privateKey.Y)

	// 压缩公钥
	compressedPubKey := compressP256PublicKey(privateKey.X, privateKey.Y)

	return hex.EncodeToString(privKeyBytes),
		hex.EncodeToString(pubKeyBytes),
		hex.EncodeToString(compressedPubKey),
		nil
}

func (c *Secp256r1Curve) Sign(privateKey, messageHash string) (string, error) {
	privByte, err := hex.DecodeString(privateKey)
	if err != nil {
		return "", err
	}

	hashByte, err := hex.DecodeString(messageHash)
	if err != nil {
		return "", err
	}

	// 构造私钥
	privKeyBigInt := new(big.Int).SetBytes(privByte)
	privKeyEcdsa := &ecdsa.PrivateKey{
		PublicKey: ecdsa.PublicKey{
			Curve: elliptic.P256(),
		},
		D: privKeyBigInt,
	}
	privKeyEcdsa.PublicKey.X, privKeyEcdsa.PublicKey.Y = elliptic.P256().ScalarBaseMult(privByte)

	// 签名
	r, s, err := ecdsa.Sign(rand.Reader, privKeyEcdsa, hashByte)
	if err != nil {
		return "", err
	}

	// 组合r和s为64字节签名
	signature := make([]byte, 64)
	copy(signature[0:32], r.Bytes())
	copy(signature[32:64], s.Bytes())

	return hex.EncodeToString(signature), nil
}

func (c *Secp256r1Curve) Verify(publicKey, messageHash, signature string) (bool, error) {
	pubKeyBytes, err := hex.DecodeString(publicKey)
	if err != nil {
		return false, err
	}

	hashBytes, err := hex.DecodeString(messageHash)
	if err != nil {
		return false, err
	}

	sigBytes, err := hex.DecodeString(signature)
	if err != nil {
		return false, err
	}

	// 解析公钥
	x, y := elliptic.Unmarshal(elliptic.P256(), pubKeyBytes)
	if x == nil {
		return false, fmt.Errorf("invalid public key")
	}

	pubKey := &ecdsa.PublicKey{
		Curve: elliptic.P256(),
		X:     x,
		Y:     y,
	}

	// 解析签名
	r := new(big.Int).SetBytes(sigBytes[0:32])
	s := new(big.Int).SetBytes(sigBytes[32:64])

	return ecdsa.Verify(pubKey, hashBytes, r, s), nil
}

func (c *Secp256r1Curve) GetCurveInfo() CurveInfo {
	return CurveInfo{
		Name:          "secp256r1 (NIST P-256)",
		KeySize:       256,
		SecurityLevel: 128,
		OID:           "1.2.840.10045.3.1.7",
		Description:   "NIST标准曲线，企业和政府广泛使用，FIPS认证",
	}
}

// 压缩P256公钥的辅助函数
func compressP256PublicKey(x, y *big.Int) []byte {
	compressed := make([]byte, 33)
	copy(compressed[1:], x.Bytes())

	// 根据y的奇偶性设置前缀
	if y.Bit(0) == 0 {
		compressed[0] = 0x02 // 偶数
	} else {
		compressed[0] = 0x03 // 奇数
	}

	return compressed
}

// CurveFactory 椭圆曲线工厂
type CurveFactory struct{}

func (f *CurveFactory) CreateCurve(curveType CurveType) (EllipticCurve, error) {
	switch curveType {
	case SECP256K1:
		return &Secp256k1Curve{}, nil
	case SECP256R1:
		return &Secp256r1Curve{}, nil
	default:
		return nil, fmt.Errorf("unsupported curve type: %s", curveType)
	}
}

func (f *CurveFactory) GetSupportedCurves() []CurveType {
	return []CurveType{SECP256K1, SECP256R1}
}

// 演示多椭圆曲线支持
func main() {
	fmt.Println("=== 多椭圆曲线支持演示 ===\n")

	factory := &CurveFactory{}
	curves := factory.GetSupportedCurves()

	message := "Hello, Multi-Curve Cryptography!"
	messageHash := sha256.Sum256([]byte(message))
	messageHashHex := hex.EncodeToString(messageHash[:])

	fmt.Printf("测试消息: %s\n", message)
	fmt.Printf("消息哈希: %s\n\n", messageHashHex)

	for _, curveType := range curves {
		fmt.Printf("=== %s 测试 ===\n", curveType)

		// 创建曲线实例
		curve, err := factory.CreateCurve(curveType)
		if err != nil {
			fmt.Printf("创建曲线失败: %v\n", err)
			continue
		}

		// 显示曲线信息
		info := curve.GetCurveInfo()
		fmt.Printf("曲线名称: %s\n", info.Name)
		fmt.Printf("密钥长度: %d 位\n", info.KeySize)
		fmt.Printf("安全强度: %d 位\n", info.SecurityLevel)
		fmt.Printf("OID: %s\n", info.OID)
		fmt.Printf("描述: %s\n\n", info.Description)

		// 生成密钥对
		privateKey, publicKey, compressedPublicKey, err := curve.GenerateKeyPair()
		if err != nil {
			fmt.Printf("密钥生成失败: %v\n", err)
			continue
		}

		fmt.Printf("私钥: %s\n", privateKey)
		fmt.Printf("公钥: %s\n", publicKey)
		fmt.Printf("压缩公钥: %s\n\n", compressedPublicKey)

		// 签名
		signature, err := curve.Sign(privateKey, messageHashHex)
		if err != nil {
			fmt.Printf("签名失败: %v\n", err)
			continue
		}

		fmt.Printf("签名: %s\n", signature)

		// 验证签名
		valid, err := curve.Verify(publicKey, messageHashHex, signature)
		if err != nil {
			fmt.Printf("验证失败: %v\n", err)
			continue
		}

		fmt.Printf("签名验证: %t\n", valid)

		// 性能和大小对比
		fmt.Printf("\n大小对比:\n")
		fmt.Printf("  私钥: %d 字节\n", len(privateKey)/2)
		fmt.Printf("  公钥: %d 字节\n", len(publicKey)/2)
		fmt.Printf("  压缩公钥: %d 字节\n", len(compressedPublicKey)/2)
		fmt.Printf("  签名: %d 字节\n", len(signature)/2)

		fmt.Println("\n" + strings.Repeat("-", 60) + "\n")
	}

	// 对比总结
	fmt.Println("=== 椭圆曲线对比总结 ===")
	fmt.Println("secp256k1:")
	fmt.Println("  ✅ 区块链生态标准")
	fmt.Println("  ✅ 性能优异")
	fmt.Println("  ✅ 社区支持丰富")
	fmt.Println("  ❌ 非政府标准")

	fmt.Println("\nsecp256r1 (NIST P-256):")
	fmt.Println("  ✅ 政府/企业标准")
	fmt.Println("  ✅ FIPS认证")
	fmt.Println("  ✅ 广泛的合规支持")
	fmt.Println("  ❌ 区块链生态支持有限")

	fmt.Println("\n推荐使用场景:")
	fmt.Println("  钱包签名服务 → secp256k1")
	fmt.Println("  企业PKI系统 → secp256r1")
	fmt.Println("  政府合规项目 → secp256r1")
}
